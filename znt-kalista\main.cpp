#define SDK_IMPLEMENTATION
#include "main.h"

#include "config.h"
#include "helpers.h"
#include "sdk.h"

namespace kalista {
    void on_update(void*) {
        if (player->is_dead() || !config->enabled)
            return;

        f32  time = get_time();
        auto mode = get_action_mode();

        if (next_cast > time) {
            return;
        }

        bool someone_has_rend = false;
        for (auto& enemy : get_enemy_heroes()) {
            if (enemy->has_buff(SpellHash("KalistaExpungeMarker"))) {
                if (is_valid_target(enemy, E_RANGE)) {
                    someone_has_rend = true;
                }
            }
        }

        for (auto& minion : get_minions()) {
            if (is_valid_minion(minion, E_RANGE) && minion->is_epic()) {
                if (calc_e_damage(minion) >= minion->health()) {
                    kalista_e->cast_self();
                }
            }
        }

        // minion rend resets
        if (config->use_e && kalista_e->is_ready()) {
            for (const auto& minion : get_minions()) {
                if (is_valid_minion(minion, E_RANGE)) {
                    if (calc_e_damage(minion) >= minion->health() && someone_has_rend) {
                        kalista_e->cast_self();
                    }
                }
            }
        }

        if (config->use_e && kalista_e->is_ready() && (mode.is(ActionMode::Combo) || config->use_e_auto)) {
            for (const auto& enemy : get_enemy_heroes()) {
                if (is_valid_target(enemy, E_RANGE)) {
                    if (calc_e_damage(enemy) >= enemy->health()) {
                        kalista_e->cast_self();
                    }
                }
            }
        }

        if (config->farm_use_e && kalista_e->is_ready()) {
            if (mode.is(ActionMode::Farm) || mode.is(ActionMode::FastFarm) || mode.is(ActionMode::LastHit)) {
                auto killable_minion_with_rend = 0;
                for (const auto& minion : get_minions()) {
                    if (is_valid_minion(minion, E_RANGE)) {
                        if (calc_e_damage(minion) >= minion->health()) {
                            killable_minion_with_rend++;
                        }
                    }
                }

                if (killable_minion_with_rend >= 3 || (killable_minion_with_rend >= 1 && player->level() < 7)) {
                    kalista_e->cast_self();
                }
            }
        }

        if (config->use_q && kalista_q->is_ready() && mode.is(ActionMode::Combo)) {
            ts::TargetOptions options = {.range = Q_RANGE, .is_valid = nullptr};
            auto              ts      = ts::get_pred_target(options, pred_q, [](const PredResult& pred) { return pred.hit_chance >= 1; });

            if (ts.target && is_valid_target(ts.target)) {
                if (!is_attack_cooldown() || !in_range(ts.target->position(), player->auto_attack_range() + 25)) {
                    if (kalista_q->cast_position(ts.pred.cast_position)) {
                        next_cast = time + CAST_RATE;
                        return;
                    }
                }
            }
        }
    }

    void on_draw_ground(void*) {
        if (player->is_dead() || !config->enabled)
            return;
    }

    void on_draw_hud(void*) {
        if (player->is_dead() || !config->enabled) {
            return;
        }

        if (config->render_damage_indicator) {
            for (const auto& enemy : get_enemy_heroes()) {
                if (!is_renderable(enemy)) {
                    // Draw the damage indicator
                    const f32 e_damage = calc_e_damage(enemy);
                    draw_damage_indicator(e_damage, enemy);
                }
            }
        }
    }

    void on_validate_and_cast_spell(OnValidateAndCastSpellhEvent* event) {
        if (player->is_dead() || !config->enabled) {
            return;
        }

        auto spell = event->spell_data->spell_hash();
        if (spell == SpellHash("KalistaMysticShot")) {
            if (player->path_controller()->is_dashing()) {
                *(event->prevent) = true;
            }
        }
    }

    void on_start_spell_cast(OnStartSpellCastEvent* event) {
        if (player->is_dead() || !config->enabled) {
            return;
        }

        if (event->caster != player) {
            return;
        }

        if (event->spell_cast_info->spell_hash() == SpellHash("KalistaMysticShot")) {
            auto pos = player->position().extend(get_world_cursor(), 300);
            move(pos);
        }
    }

    void on_load() {
        player = get_player();
        if (!player->is("Kalista")) {
            return;
        }

        kalista_q = player->get_spell(SpellSlot::Q);
        kalista_w = player->get_spell(SpellSlot::W);
        kalista_e = player->get_spell(SpellSlot::E);
        kalista_r = player->get_spell(SpellSlot::R);
        config    = new Config();

        register_module("[Kurisu] Kalista++",
            {.on_draw_ground                = on_draw_ground,
                .on_draw_hud                = on_draw_hud,
                .on_update                  = on_update,
                .on_draw_menu               = on_draw_menu,
                .on_validate_and_cast_spell = on_validate_and_cast_spell,
                .on_start_spell_cast        = on_start_spell_cast});
    }
}  // namespace kalista

ENTRY({ kalista::on_load(); });
