#pragma once
#include "config.h"
#include "sdk.h"

namespace kalista {
    inline static f32 time      = 0.0f;
    inline static f32 next_cast = 0.0f;

    inline static AIHeroClient*  player;
    inline static SpellInstance* kalista_q;
    inline static SpellInstance* kalista_w;
    inline static SpellInstance* kalista_e;
    inline static SpellInstance* kalista_r;

    constexpr f32 CAST_RATE = 0.25f;
    constexpr f32 E_RANGE   = 1100.0f;

    constexpr f32 Q_RANGE = 1200.0f;
    constexpr f32 Q_WIDTH = 80.0f;
    constexpr f32 Q_SPEED = 2400.0f;
    constexpr f32 Q_DELAY = 0.25f;

    // Champion theme color constants
    constexpr u8 THEME_RED   = 147;  // Light purple red component
    constexpr u8 THEME_GREEN = 112;  // Light purple green component
    constexpr u8 THEME_BLUE  = 219;  // Light purple blue component
    constexpr u8 THEME_ALPHA = 200;  // Semi-transparent

    inline static f32 calc_e_damage(AIBaseClient* target) {
        f32 damage = 0.0f;
        if (!target) {
            return damage;
        }

        i32 spell_level = kalista_e->level();
        if (spell_level == 0) {
            return damage;
        }

        // E Base Damage: 5/15/25/35/45 (+70% AD) (+20% AP)
        f32 raw_rend_damage[]            = {20.0f, 30.0f, 49.0f, 50.0f, 60.0f};
        f32 raw_rend_damage_multiplier[] = {0.60f, 0.60f, 0.60f, 0.60f, 0.60f};

        f32 raw_rend_damage_per_stack[]            = {10.0f, 14.0f, 19.0f, 25.0f, 32.0f};
        f32 raw_rend_damage_per_stack_multiplier[] = {0.20f, 0.25f, 0.30f, 0.35f, 0.40f};

        f32 player_ad = player->physical_damage();
        f32 player_ap = player->magical_damage();

        damage = raw_rend_damage[spell_level - 1] + (player_ad * raw_rend_damage_multiplier[spell_level - 1]) + (player_ap * 0.20f);

        auto e_buff = target->get_buff(SpellHash("KalistaExpungeMarker"));
        if (!e_buff) {
            return player->calc_physical_damage(target, damage);
        }

        i32 stacks = e_buff->count();

        // Damage from additional spears
        if (stacks > 1) {
            i32 additional_spears = stacks - 1;
            damage += additional_spears * (raw_rend_damage_per_stack[spell_level - 1] +
                                              (player_ad * raw_rend_damage_per_stack_multiplier[spell_level - 1]) + (player_ap * 0.20f));
        }

        // Rend deals 50% damage against epic monsters
        if (target->is_minion()) {
            auto minion = target->as_minion();
            if (minion->is_epic()) {
                damage *= 0.5f;
            }
        }

        return player->calc_physical_damage(target, damage);
    }

};  // namespace kalista