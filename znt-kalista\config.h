#pragma once
#include "sdk.h"

namespace kalista {
    struct Config {
        MENU_PAGE(MAIN_PAGE, "kurisu:kalista");
        MENU_TOGGLE(ENABLED, "kurisu:kalista:enabled", enabled, true);

        MENU_PAGE(COMBO_PAGE, "kurisu:kalista:combo");
        MENU_TOGGLE(COMBO_USE_Q, "kurisu:kalista:combo:use:q", use_q, true);
        MENU_TOGGLE(COMBO_USE_Q_AUTO, "kurisu:kalista:combo:use:q:auto", use_q_auto, true);
        MENU_TOGGLE(COMBO_USE_E, "kurisu:kalista:combo:use:e", use_e, true);
        MENU_TOGGLE(COMBO_USE_E_AUTO, "kurisu:kalista:combo:use:e:auto", use_e_auto, true);

        MENU_PAGE(HARASS_PAGE, "kurisu:kalista:harass");
        MENU_SLIDER_I32(HARASS_MIN_MANA_PERCENT, "kurisu:kalista:harass:min:mana:percent", harass_min_mana_percent, 65);
        MENU_TOGGLE(HARASS_USE_Q, "kurisu:kalista:harass:use:q", harass_use_q, true);
        MENU_TOGGLE(HARASS_USE_E, "kurisu:kalista:harass:use:e", harass_use_e, false);

        MENU_PAGE(FARM_PAGE, "kurisu:kalista:farm");
        MENU_SLIDER_I32(FARM_MIN_MANA_PERCENT, "kurisu:kalista:farm:min:mana:percent", farm_min_mana_percent, 15);
        MENU_TOGGLE(FARM_USE_Q, "kurisu:kalista:farm:use:q", farm_use_q, true);
        MENU_TOGGLE(FARM_USE_E, "kurisu:kalista:farm:use:e", farm_use_e, true);

        MENU_PAGE(MECHANICS_PAGE, "kurisu:zeri:mechanics");

        MENU_PAGE(RENDER_PAGE, "kurisu:zeri:render");
        MENU_TOGGLE(RENDER_Q_RANGE, "kurisu:zeri:render:q:range", render_q_range, true);
        MENU_TOGGLE(RENDER_E_RANGE, "kurisu:zeri:render:e:range", render_e_range, true);
        MENU_TOGGLE(RENDER_DAMAGE_INDICATOR, "kurisu:zeri:render:damage_indicator", render_damage_indicator, true);

        void draw_menu(MenuContext* ctx) {
            const MenuPage page = ctx->current_page();
            if (page == MenuContext::PAGE_ROOT) {
                ctx->submenu(MAIN_PAGE, "[Kurisu] Kalista");
                return;
            }

            if (page == MAIN_PAGE) {
                ctx->title("[Kurisu] Kalista v1.0");
                ctx->toggle(ENABLED, "Enable Plugin", &enabled);
                ctx->submenu(COMBO_PAGE, "Kalista: Combo");
                ctx->submenu(HARASS_PAGE, "Kalista: Harass");
                ctx->submenu(FARM_PAGE, "Kalista: Farm");
                ctx->submenu(MECHANICS_PAGE, "Kalista: Mechanics");
                ctx->submenu(RENDER_PAGE, "Kalista: Render");
            }

            if (page == COMBO_PAGE) {
                ctx->title("Kalista: Combo");
                ctx->toggle(COMBO_USE_Q, "Use Q", &use_q);
                ctx->toggle(COMBO_USE_Q_AUTO, "-> Secure Kill", &use_q_auto);
                ctx->toggle(COMBO_USE_E, "Use E", &use_e);
                ctx->toggle(COMBO_USE_E_AUTO, "-> Secure Kill", &use_e_auto);
            }

            if (page == HARASS_PAGE) {
                ctx->title("Kalista: Harass");
                ctx->slider(HARASS_MIN_MANA_PERCENT, "Min Mana % for Harass", 0, 100, 1, &harass_min_mana_percent, true);
                ctx->toggle(HARASS_USE_Q, "Use Q", &harass_use_q);
                ctx->toggle(HARASS_USE_E, "Use E", &harass_use_e);
            }

            if (page == FARM_PAGE) {
                ctx->title("    Kalista: Farm");
                ctx->toggle(FARM_USE_Q, "Use Q", &farm_use_q);
                ctx->toggle(FARM_USE_E, "Use E (Farm)", &farm_use_e);
            }

            if (page == MECHANICS_PAGE) {
                ctx->title("Zeri: Mechanics");
            }

            if (page == RENDER_PAGE) {
                ctx->title("Zeri: Render");
                ctx->toggle(RENDER_Q_RANGE, "Draw Q", &render_q_range);
                ctx->toggle(RENDER_E_RANGE, "Draw E", &render_e_range);
                ctx->toggle(RENDER_DAMAGE_INDICATOR, "Draw Damage Indicator", &render_damage_indicator);
            }
        }
    };

    inline static Config* config = nullptr;

    static void on_draw_menu(MenuContext* ctx) {
        config->draw_menu(ctx);
    }
};  // namespace kalista
