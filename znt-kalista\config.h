/*
 * config.h - Configuration system for Kalista bot
 *
 * This file defines the configuration structure and menu system for the Kalista bot.
 * It handles all user-configurable settings including combo options, harass settings,
 * farming preferences, mechanics, and visual rendering options.
 *
 * The config uses the SDK's menu system to provide an in-game interface for
 * adjusting bot behavior without needing to restart or recompile.
 */

#pragma once
#include "sdk.h"

namespace kalista {
    struct Config {
        // ========================================
        // MAIN PAGE - Root configuration
        // ========================================
        MENU_PAGE(MAIN_PAGE, "kurisu:kalista");
        MENU_TOGGLE(ENABLED, "kurisu:kalista:enabled", enabled, true);

        // ========================================
        // COMBO SETTINGS - Full damage rotation
        // ========================================
        MENU_PAGE(COMBO_PAGE, "kurisu:kalista:combo");
        MENU_TOGGLE(COMBO_USE_Q, "kurisu:kalista:combo:use:q", use_q, true);
        MENU_TOGGLE(COMBO_USE_Q_AUTO, "kurisu:kalista:combo:use:q:auto", use_q_auto, true);
        MENU_TOGGLE(COMBO_USE_E, "kurisu:kalista:combo:use:e", use_e, true);
        MENU_TOGGLE(COMBO_USE_E_AUTO, "kurisu:kalista:combo:use:e:auto", use_e_auto, true);

        // ========================================
        // HARASS SETTINGS - Poke and trade
        // ========================================
        MENU_PAGE(HARASS_PAGE, "kurisu:kalista:harass");
        MENU_SLIDER_I32(HARASS_MIN_MANA_PERCENT, "kurisu:kalista:harass:min:mana:percent", harass_min_mana_percent, 65);
        MENU_TOGGLE(HARASS_USE_Q, "kurisu:kalista:harass:use:q", harass_use_q, true);
        MENU_TOGGLE(HARASS_USE_E, "kurisu:kalista:harass:use:e", harass_use_e, false);

        // ========================================
        // FARMING SETTINGS - Last hitting and wave clear
        // ========================================
        MENU_PAGE(FARM_PAGE, "kurisu:kalista:farm");
        MENU_SLIDER_I32(FARM_MIN_MANA_PERCENT, "kurisu:kalista:farm:min:mana:percent", farm_min_mana_percent, 15);
        MENU_TOGGLE(FARM_USE_Q, "kurisu:kalista:farm:use:q", farm_use_q, true);
        MENU_TOGGLE(FARM_USE_E, "kurisu:kalista:farm:use:e", farm_use_e, true);

        // ========================================
        // MECHANICS - Advanced gameplay features
        // ========================================
        MENU_PAGE(MECHANICS_PAGE, "kurisu:kalista:mechanics");

        // ========================================
        // RENDERING - Visual indicators and overlays
        // ========================================
        MENU_PAGE(RENDER_PAGE, "kurisu:kalista:render");
        MENU_TOGGLE(RENDER_Q_RANGE, "kurisu:kalista:render:q:range", render_q_range, true);
        MENU_TOGGLE(RENDER_E_RANGE, "kurisu:kalista:render:e:range", render_e_range, true);
        MENU_TOGGLE(RENDER_DAMAGE_INDICATOR, "kurisu:kalista:render:damage_indicator", render_damage_indicator, true);

        // Main menu drawing function - handles all the UI logic for the in-game menu
        void draw_menu(MenuContext* ctx) {
            const MenuPage page = ctx->current_page();

            // Root menu - shows the main plugin entry point
            if (page == MenuContext::PAGE_ROOT) {
                ctx->submenu(MAIN_PAGE, "[Kurisu] Kalista");
                return;
            }

            // Main page - shows plugin info and navigation to sub-menus
            if (page == MAIN_PAGE) {
                ctx->title("[Kurisu] Kalista v1.0");
                ctx->toggle(ENABLED, "Enable Plugin", &enabled);
                ctx->submenu(COMBO_PAGE, "Kalista: Combo");
                ctx->submenu(HARASS_PAGE, "Kalista: Harass");
                ctx->submenu(FARM_PAGE, "Kalista: Farm");
                ctx->submenu(MECHANICS_PAGE, "Kalista: Mechanics");
                ctx->submenu(RENDER_PAGE, "Kalista: Render");
            }

            // Combo page - settings for full damage rotation (usually on key press)
            if (page == COMBO_PAGE) {
                ctx->title("Kalista: Combo");
                ctx->toggle(COMBO_USE_Q, "Use Q (Pierce)", &use_q);
                ctx->toggle(COMBO_USE_Q_AUTO, "-> Auto Q for Kills", &use_q_auto);
                ctx->toggle(COMBO_USE_E, "Use E (Rend)", &use_e);
                ctx->toggle(COMBO_USE_E_AUTO, "-> Auto E for Kills", &use_e_auto);
            }

            // Harass page - settings for poking enemies without going all-in
            if (page == HARASS_PAGE) {
                ctx->title("Kalista: Harass");
                ctx->slider(HARASS_MIN_MANA_PERCENT, "Min Mana % for Harass", 0, 100, 1, &harass_min_mana_percent, true);
                ctx->toggle(HARASS_USE_Q, "Use Q (Pierce)", &harass_use_q);
                ctx->toggle(HARASS_USE_E, "Use E (Rend)", &harass_use_e);
            }

            // Farm page - settings for last hitting minions and wave clearing
            if (page == FARM_PAGE) {
                ctx->title("Kalista: Farm");
                ctx->slider(FARM_MIN_MANA_PERCENT, "Min Mana % for Farm", 0, 100, 1, &farm_min_mana_percent, true);
                ctx->toggle(FARM_USE_Q, "Use Q (Pierce)", &farm_use_q);
                ctx->toggle(FARM_USE_E, "Use E (Rend)", &farm_use_e);
            }

            // Mechanics page - advanced features like auto-dodge, kiting, etc.
            if (page == MECHANICS_PAGE) {
                ctx->title("Kalista: Mechanics");
                // TODO: Add mechanics settings here
            }

            // Render page - visual indicators and drawing options
            if (page == RENDER_PAGE) {
                ctx->title("Kalista: Render");
                ctx->toggle(RENDER_Q_RANGE, "Draw Q Range", &render_q_range);
                ctx->toggle(RENDER_E_RANGE, "Draw E Range", &render_e_range);
                ctx->toggle(RENDER_DAMAGE_INDICATOR, "Draw Damage Indicator", &render_damage_indicator);
            }
        }
    };

    // Global config instance - this gets initialized when the plugin loads
    // All other parts of the bot will access settings through this pointer
    inline static Config* config = nullptr;

    // Menu callback function - called by the SDK when the menu needs to be drawn
    // This just forwards the call to our config's draw_menu function
    static void on_draw_menu(MenuContext* ctx) {
        config->draw_menu(ctx);
    }
};  // namespace kalista
