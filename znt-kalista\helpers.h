#pragma once
#include "main.h"
#include "sdk.h"

namespace kalista {

    inline static bool in_range(Position pos, f32 range) {
        return get_player()->position().distance(pos) <= range;
    }

    // Checks if a target is valid (not dead, targetable, visible, etc.) and optionally within a specified range.
    inline static bool is_valid_target(AIBaseClient* target, f32 range = FLT_MAX) {
        if (!target) {
            return false;
        }

        if (target->is_dead() || !target->is_targetable() || !target->is_visible() || target->is_invulnerable() || target->is_zombie()) {
            return false;
        }

        // Check if target is within range
        return in_range(target->position(), range);
    }

    // Checks if a given AIMinionClient pointer is valid and represents a standard lane/jungle minion.
    // Excludes pets, plants, wards, and traps from being considered valid minions for certain logic (e.g., farming).
    inline static bool is_valid_minion(AIMinionClient* minion, f32 range = FLT_MAX) {
        if (!minion) {
            return false;
        }

        if (minion->name() == SmallStr("Barrel")) {
            return false;
        }

        return !minion->is_ally() && !minion->is_trap() && is_valid_target(minion, range);
    }

    // Draws a damage indicator on enemy health bars
    inline static void draw_damage_indicator(f32 damage, AIBaseClient* enemy) {
        if (enemy->is_dead() || !enemy->is_visible() || !enemy->is_on_screen())
            return;

        // Get enemy's health bar position
        const auto bar = get_resource_bar(enemy);

        // Calculate damage percentage relative to current health, capped at 100% for overkill
        const f32 current_health = enemy->health();
        const f32 damage_percent = (current_health > 0.0f) ? min(1.0f, damage / current_health) : 0.0f;

        // Draw the damage indicator as a fill on the health bar using theme color
        const f32 bar_width    = bar.right - bar.left;
        const f32 damage_width = min(bar_width * damage_percent, bar_width);  // Clamp to bar width

        // Calculate the starting position from the right side, ensuring we don't go past the left edge
        const f32 start_x = max(bar.left, bar.right - damage_width);

        // Draw the damage indicator with the theme color, starting from the right
        renderer::draw_rect_2d_fill(
            Vec2(start_x, bar.top), Vec2(damage_width, bar.bot - bar.top), Color(THEME_RED, THEME_GREEN, THEME_BLUE, THEME_ALPHA));
    }

    inline static bool under_enemy_turret(Position pos) {
        for (auto turret : get_enemy_turrets()) {
            if (turret->is_dead() || !turret->is_visible() || !turret->is_on_screen())
                continue;

            if (turret->position().distance(pos) < 1000) {
                return true;
            }
        }

        return false;
    }

    inline static bool under_ally_turret(Position pos) {
        for (auto turret : get_ally_turrets()) {
            if (turret->is_dead() || !turret->is_visible() || !turret->is_on_screen())
                continue;

            if (turret->position().distance(pos) < 1000) {
                return true;
            }
        }

        return false;
    }
}  // namespace kalista