/*
 * helpers.h - Utility functions for Kalista bot implementation
 *
 * This file contains a collection of helper functions that are commonly used
 * throughout the Kalista bot. These utilities handle things like checking if
 * targets are valid, calculating ranges, drawing damage indicators on health bars,
 * and determining if positions are safe from turrets.
 *
 * All functions are inline and static for performance.
 */

#pragma once
#include "main.h"
#include "sdk.h"

namespace kalista {

    // Simple helper to check if a position is within range of our player
    // Takes a position and max distance, returns true if we're close enough
    inline static bool in_range(Position pos, f32 range) {
        return get_player()->position().distance(pos) <= range;
    }

    // Checks if a target is renderable - not dead, visible, and on screen
    // Useful for drawing indicators or other UI elements
    inline static bool is_renderable(AIBaseClient* target) {
        return !target->is_dead() && target->is_visible() && target->is_on_screen();
    }

    // Checks if a target is worth attacking - makes sure it's not dead, invisible, invulnerable, etc.
    // Also optionally checks if it's within a certain range (defaults to unlimited range)
    // This is the main function we use before trying to attack anything
    inline static bool is_valid_target(AIBaseClient* target, f32 range = FLT_MAX) {
        if (!target) {
            return false;
        }

        // Skip targets that are dead, untargetable, invisible, invulnerable, or zombie state
        if (target->is_dead() || !target->is_targetable() || !target->is_visible() || target->is_invulnerable() || target->is_zombie()) {
            return false;
        }

        // Check if target is within range
        return in_range(target->position(), range);
    }

    // Checks if a minion is good for farming - filters out pets, wards, traps, etc.
    // We specifically exclude Gangplank barrels and only want enemy minions
    // Uses the same target validation as above but with extra minion-specific checks
    inline static bool is_valid_minion(AIMinionClient* minion, f32 range = FLT_MAX) {
        if (!minion) {
            return false;
        }

        // Don't target Gangplank barrels - they're not real minions
        if (minion->name() == SmallStr("Barrel")) {
            return false;
        }

        // Only target enemy minions that aren't traps and pass our general target validation
        return !minion->is_ally() && !minion->is_trap() && is_valid_target(minion, range);
    }

    // Draws a colored bar on enemy health bars to show how much damage we can deal
    // The bar appears on the right side of their health bar and shows damage as a percentage
    // Really useful for knowing if you can kill someone or not
    inline static void draw_damage_indicator(f32 damage, AIBaseClient* enemy) {
        if (enemy->is_dead() || !enemy->is_visible() || !enemy->is_on_screen())
            return;

        // Get enemy's health bar position
        const auto bar = get_resource_bar(enemy);

        // Calculate damage percentage relative to current health, capped at 100% for overkill
        const f32 current_health = enemy->health();
        const f32 damage_percent = (current_health > 0.0f) ? min(1.0f, damage / current_health) : 0.0f;

        // Draw the damage indicator as a fill on the health bar using theme color
        const f32 bar_width    = bar.right - bar.left;
        const f32 damage_width = min(bar_width * damage_percent, bar_width);  // Clamp to bar width

        // Calculate the starting position from the right side, ensuring we don't go past the left edge
        const f32 start_x = max(bar.left, bar.right - damage_width);

        // Draw the damage indicator with the theme color, starting from the right
        renderer::draw_rect_2d_fill(
            Vec2(start_x, bar.top), Vec2(damage_width, bar.bot - bar.top), Color(THEME_RED, THEME_GREEN, THEME_BLUE, THEME_ALPHA));
    }

    // Checks if a position is dangerous because it's too close to enemy turrets
    // Turrets have about 1000 range so we check all enemy turrets for that distance
    // Useful for knowing if we should avoid a certain area
    inline static bool under_enemy_turret(Position pos) {
        for (auto turret : get_enemy_turrets()) {
            if (turret->is_dead() || !turret->is_visible() || !turret->is_on_screen())
                continue;

            if (turret->position().distance(pos) < 1000) {
                return true;
            }
        }

        return false;
    }

    // Checks if a position is safe because it's protected by our turrets
    // Same 1000 range as enemy turrets, but this time we want to be IN range for safety
    // Good for knowing if we can fight safely or if we should retreat to turret range
    inline static bool under_ally_turret(Position pos) {
        for (auto turret : get_ally_turrets()) {
            if (turret->is_dead() || !turret->is_visible() || !turret->is_on_screen())
                continue;

            if (turret->position().distance(pos) < 1000) {
                return true;
            }
        }

        return false;
    }
}  // namespace kalista