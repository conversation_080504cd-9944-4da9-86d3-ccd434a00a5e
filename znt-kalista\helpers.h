/**
 * @file helpers.h
 * @brief Utility functions for Kalista bot implementation
 *
 * This header provides a collection of helper functions commonly used throughout
 * the Kalista bot implementation. These utilities handle target validation,
 * range checking, damage visualization, and positioning logic.
 *
 * <AUTHOR>
 * @namespace kalista
 */

#pragma once
#include "main.h"
#include "sdk.h"

namespace kalista {

    /**
     * @brief Checks if a position is within a specified range of the player
     *
     * @param pos The position to check
     * @param range The maximum distance allowed
     * @return true if the position is within range, false otherwise
     */
    inline static bool in_range(Position pos, f32 range) {
        return get_player()->position().distance(pos) <= range;
    }

    /**
     * @brief Validates if a target is suitable for interaction
     *
     * @param target Pointer to the target to validate
     * @param range Maximum distance from player (default: FLT_MAX for no range limit)
     * @return true if target passes all validation checks, false otherwise
     */
    inline static bool is_valid_target(AIBaseClient* target, f32 range = FLT_MAX) {
        if (!target) {
            return false;
        }

        if (target->is_dead() || !target->is_targetable() || !target->is_visible() || target->is_invulnerable() || target->is_zombie()) {
            return false;
        }

        // Check if target is within range
        return in_range(target->position(), range);
    }

    /**
     * @brief Validates if a minion is suitable for farming/targeting
     *
     * Checks if a given AIMinionClient pointer is valid and represents a standard
     * lane/jungle minion. Excludes pets, plants, wards, traps, and special objects
     * from being considered valid minions for farming logic.
     *
     * @param minion Pointer to the minion to validate
     * @param range Maximum distance from player (default: FLT_MAX for no range limit)
     * @return true if minion is a valid enemy minion for farming, false otherwise
     */
    inline static bool is_valid_minion(AIMinionClient* minion, f32 range = FLT_MAX) {
        if (!minion) {
            return false;
        }

        if (minion->name() == SmallStr("Barrel")) {
            return false;
        }

        return !minion->is_ally() && !minion->is_trap() && is_valid_target(minion, range);
    }

    /**
     * @brief Draws a visual damage indicator on enemy health bars
     *
     * Renders a colored overlay on an enemy's health bar to show potential damage.
     * The indicator appears as a filled rectangle starting from the right side of
     * the health bar, representing the percentage of damage relative to current health.
     *
     * @param damage The amount of damage to visualize
     * @param enemy Pointer to the enemy target whose health bar to draw on
     */
    inline static void draw_damage_indicator(f32 damage, AIBaseClient* enemy) {
        if (enemy->is_dead() || !enemy->is_visible() || !enemy->is_on_screen())
            return;

        // Get enemy's health bar position
        const auto bar = get_resource_bar(enemy);

        // Calculate damage percentage relative to current health, capped at 100% for overkill
        const f32 current_health = enemy->health();
        const f32 damage_percent = (current_health > 0.0f) ? min(1.0f, damage / current_health) : 0.0f;

        // Draw the damage indicator as a fill on the health bar using theme color
        const f32 bar_width    = bar.right - bar.left;
        const f32 damage_width = min(bar_width * damage_percent, bar_width);  // Clamp to bar width

        // Calculate the starting position from the right side, ensuring we don't go past the left edge
        const f32 start_x = max(bar.left, bar.right - damage_width);

        // Draw the damage indicator with the theme color, starting from the right
        renderer::draw_rect_2d_fill(
            Vec2(start_x, bar.top), Vec2(damage_width, bar.bot - bar.top), Color(THEME_RED, THEME_GREEN, THEME_BLUE, THEME_ALPHA));
    }

    /**
     * @brief Checks if a position is within enemy turret range
     *
     * Iterates through all enemy turrets and determines if the given position
     * falls within their attack range. Uses a fixed range of 1000 units which
     * represents the typical turret attack range.
     *
     * @param pos The position to check
     * @return true if position is within range of any active enemy turret, false otherwise
     */
    inline static bool under_enemy_turret(Position pos) {
        for (auto turret : get_enemy_turrets()) {
            if (turret->is_dead() || !turret->is_visible() || !turret->is_on_screen())
                continue;

            if (turret->position().distance(pos) < 1000) {
                return true;
            }
        }

        return false;
    }

    /**
     * @brief Checks if a position is within allied turret range
     *
     * Iterates through all allied turrets and determines if the given position
     * falls within their protection range. Uses a fixed range of 1000 units which
     * represents the typical turret attack range.
     *
     * @param pos The position to check
     * @return true if position is within range of any active allied turret, false otherwise
     */
    inline static bool under_ally_turret(Position pos) {
        for (auto turret : get_ally_turrets()) {
            if (turret->is_dead() || !turret->is_visible() || !turret->is_on_screen())
                continue;

            if (turret->position().distance(pos) < 1000) {
                return true;
            }
        }

        return false;
    }
}  // namespace kalista