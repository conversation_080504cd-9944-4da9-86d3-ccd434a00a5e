{"C_Cpp.default.cppStandard": "c++23", "C_Cpp.default.cStandard": "c23", "C_Cpp.intelliSenseEngine": "default", "C_Cpp.default.intelliSenseMode": "windows-clang-x64", "C_Cpp.default.compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/bin/clang-cl.exe", "C_Cpp.default.configurationProvider": "ms-vscode.cpptools", "editor.formatOnSave": true, "clang-format.executable": "C:/Program Files/LLVM/bin/clang-format.exe"}